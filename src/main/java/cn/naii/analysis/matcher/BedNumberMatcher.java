package cn.naii.analysis.matcher;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 床位号匹配器
 * 实现床位号的模糊匹配，支持"床位1"和"1床"等格式的匹配
 */
public class BedNumberMatcher implements FieldMatcher {
    
    private static final Pattern BED_NUMBER_PATTERN = Pattern.compile("(\\d+)");
    
    @Override
    public boolean matches(String labelValue, String predictValue) {
        if (isEmptyValue(labelValue) && isEmptyValue(predictValue)) {
            return true;
        }
        if (isEmptyValue(labelValue) || isEmptyValue(predictValue)) {
            return false;
        }
        
        return isBedMatch(labelValue, predictValue);
    }
    
    @Override
    public String getFieldType() {
        return "床位";
    }
    
    /**
     * 判断床位号是否匹配
     * 支持"床位1"和"1床"等格式的匹配
     */
    private boolean isBedMatch(String bed1, String bed2) {
        // 直接相等
        if (bed1.equals(bed2)) {
            return true;
        }
        
        // 提取数字部分进行比较
        String number1 = extractBedNumber(bed1);
        String number2 = extractBedNumber(bed2);
        
        if (number1 != null && number2 != null) {
            return number1.equals(number2);
        }
        
        // 如果无法提取数字，则进行字符串包含匹配
        return bed1.contains(bed2) || bed2.contains(bed1);
    }
    
    /**
     * 从床位字符串中提取数字部分
     */
    private String extractBedNumber(String bedString) {
        Matcher matcher = BED_NUMBER_PATTERN.matcher(bedString);
        if (matcher.find()) {
            return matcher.group(1);
        }
        return null;
    }
    
    /**
     * 判断值是否为空
     */
    private boolean isEmptyValue(String value) {
        return value == null || value.trim().isEmpty() || "null".equalsIgnoreCase(value.trim());
    }
}
