package cn.naii.analysis.matcher;

/**
 * 条形码匹配器
 * 实现条形码的模糊匹配，支持前缀匹配
 */
public class BarcodeMatcher implements FieldMatcher {
    
    @Override
    public boolean matches(String labelValue, String predictValue) {
        if (isEmptyValue(labelValue) && isEmptyValue(predictValue)) {
            return true;
        }
        if (isEmptyValue(labelValue) || isEmptyValue(predictValue)) {
            return false;
        }
        
        // 条形码使用前缀匹配
        return predictValue.startsWith(labelValue) || labelValue.startsWith(predictValue);
    }
    
    @Override
    public String getFieldType() {
        return "条形码";
    }
    
    /**
     * 判断值是否为空
     */
    private boolean isEmptyValue(String value) {
        return value == null || value.trim().isEmpty() || "null".equalsIgnoreCase(value.trim());
    }
}
