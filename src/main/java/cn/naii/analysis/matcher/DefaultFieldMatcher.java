package cn.naii.analysis.matcher;

/**
 * 默认字段匹配器
 * 提供基本的字符串相等匹配
 */
public class DefaultFieldMatcher implements FieldMatcher {
    
    private final String fieldType;
    
    public DefaultFieldMatcher(String fieldType) {
        this.fieldType = fieldType;
    }
    
    @Override
    public boolean matches(String labelValue, String predictValue) {
        // 处理null值
        if (labelValue == null && predictValue == null) {
            return true;
        }
        if (labelValue == null || predictValue == null) {
            return false;
        }
        
        // 处理空字符串和"null"字符串
        if (isEmptyValue(labelValue) && isEmptyValue(predictValue)) {
            return true;
        }
        if (isEmptyValue(labelValue) || isEmptyValue(predictValue)) {
            return false;
        }
        
        // 基本字符串匹配
        return labelValue.equals(predictValue);
    }
    
    @Override
    public String getFieldType() {
        return fieldType;
    }
    
    /**
     * 判断值是否为空
     */
    protected boolean isEmptyValue(String value) {
        return value == null || value.trim().isEmpty() || "null".equalsIgnoreCase(value.trim());
    }
}
