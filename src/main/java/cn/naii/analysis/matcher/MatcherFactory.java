package cn.naii.analysis.matcher;

import java.util.HashMap;
import java.util.Map;

/**
 * 匹配器工厂类
 * 负责创建和管理各种字段匹配器
 */
public class MatcherFactory {
    
    private static final Map<String, FieldMatcher> matchers = new HashMap<>();
    
    static {
        // 初始化各种字段匹配器
        matchers.put("条形码", new BarcodeMatcher());
        matchers.put("患者姓名", new DefaultFieldMatcher("患者姓名"));
        matchers.put("年龄", new AgeMatcher());
        matchers.put("性别", new DefaultFieldMatcher("性别"));
        matchers.put("住院/门诊号", new DefaultFieldMatcher("住院/门诊号"));
        matchers.put("床位", new BedNumberMatcher());
        matchers.put("科室", new ContainsMatcher("科室"));
        matchers.put("样本类型", new ContainsMatcher("样本类型"));
        matchers.put("样本性状", new ContainsMatcher("样本性状"));
        matchers.put("采集时间", new TimeMatcher());
        matchers.put("医生", new ContainsMatcher("医生"));
        matchers.put("检测项目", new TestItemMatcher());
        matchers.put("备注栏", new DefaultFieldMatcher("备注栏"));
        matchers.put("日期", new DefaultFieldMatcher("日期"));
    }
    
    /**
     * 获取指定字段的匹配器
     * 
     * @param fieldName 字段名
     * @return 对应的字段匹配器
     */
    public static FieldMatcher getMatcher(String fieldName) {
        FieldMatcher matcher = matchers.get(fieldName);
        if (matcher == null) {
            // 如果没有专门的匹配器，返回默认匹配器
            return new DefaultFieldMatcher(fieldName);
        }
        return matcher;
    }
    
    /**
     * 注册新的字段匹配器
     * 
     * @param fieldName 字段名
     * @param matcher 匹配器实例
     */
    public static void registerMatcher(String fieldName, FieldMatcher matcher) {
        matchers.put(fieldName, matcher);
    }
    
    /**
     * 获取所有已注册的匹配器
     * 
     * @return 匹配器映射表的副本
     */
    public static Map<String, FieldMatcher> getAllMatchers() {
        return new HashMap<>(matchers);
    }
}
