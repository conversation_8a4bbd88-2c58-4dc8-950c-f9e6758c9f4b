package cn.naii.analysis.matcher;

/**
 * 时间匹配器
 * 实现采集时间的模糊匹配，只比较小时部分
 */
public class TimeMatcher implements FieldMatcher {
    
    @Override
    public boolean matches(String labelValue, String predictValue) {
        if (isEmptyValue(labelValue) && isEmptyValue(predictValue)) {
            return true;
        }
        if (isEmptyValue(labelValue) || isEmptyValue(predictValue)) {
            return false;
        }
        
        return isTimeMatch(labelValue, predictValue);
    }
    
    @Override
    public String getFieldType() {
        return "采集时间";
    }
    
    /**
     * 判断时间是否匹配
     * 只比较小时部分，允许分钟有误差
     */
    private boolean isTimeMatch(String labelValue, String predictValue) {
        try {
            // 提取小时部分进行比较
            String labelHour = extractHour(labelValue);
            String predictHour = extractHour(predictValue);
            
            if (labelHour != null && predictHour != null) {
                return labelHour.equals(predictHour);
            }
            
            // 如果无法提取小时，则直接比较字符串
            return labelValue.equals(predictValue);
        } catch (Exception e) {
            // 如果格式不正确，则直接比较字符串
            return labelValue.equals(predictValue);
        }
    }
    
    /**
     * 从时间字符串中提取小时部分
     */
    private String extractHour(String timeString) {
        if (timeString.contains(":")) {
            String[] parts = timeString.split(":");
            if (parts.length > 0) {
                return parts[0].trim();
            }
        }
        return null;
    }
    
    /**
     * 判断值是否为空
     */
    private boolean isEmptyValue(String value) {
        return value == null || value.trim().isEmpty() || "null".equalsIgnoreCase(value.trim());
    }
}
