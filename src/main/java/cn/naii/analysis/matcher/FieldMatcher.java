package cn.naii.analysis.matcher;

/**
 * 字段匹配器接口
 * 定义字段值匹配的通用接口
 */
public interface FieldMatcher {
    
    /**
     * 判断两个字段值是否匹配
     * 
     * @param labelValue 标签值
     * @param predictValue 预测值
     * @return 是否匹配
     */
    boolean matches(String labelValue, String predictValue);
    
    /**
     * 获取该匹配器支持的字段类型
     * 
     * @return 字段类型
     */
    String getFieldType();
    
    /**
     * 获取匹配器的描述信息
     * 
     * @return 描述信息
     */
    default String getDescription() {
        return "Field matcher for " + getFieldType();
    }
}
