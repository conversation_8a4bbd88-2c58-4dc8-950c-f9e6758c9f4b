package cn.naii.analysis.matcher;

import cn.naii.analysis.config.MatchingConfig;
import java.util.HashSet;
import java.util.Set;

/**
 * 检测项目匹配器
 * 实现检测项目的模糊匹配逻辑
 */
public class TestItemMatcher implements FieldMatcher {
    
    @Override
    public boolean matches(String labelValue, String predictValue) {
        if (isEmptyValue(labelValue) && isEmptyValue(predictValue)) {
            return true;
        }
        if (isEmptyValue(labelValue) || isEmptyValue(predictValue)) {
            return false;
        }
        
        return isTestItemMatch(labelValue, predictValue);
    }
    
    @Override
    public String getFieldType() {
        return "检测项目";
    }
    
    /**
     * 判断检测项目是否匹配
     * 实现模糊匹配，只要一个是另一个的子集就算匹配成功
     */
    private boolean isTestItemMatch(String item1, String item2) {
        // 如果完全相同，直接返回匹配成功
        if (item1.equals(item2)) {
            return true;
        }
        
        // 不区分大小写完全相同匹配
        if (item1.equalsIgnoreCase(item2)) {
            return true;
        }
        
        // 标准化括号后完全相同匹配
        String item1NormBrackets = normalizeBrackets(item1);
        String item2NormBrackets = normalizeBrackets(item2);
        if (item1NormBrackets.equals(item2NormBrackets)) {
            return true;
        }
        
        // 标准化括号后不区分大小写完全相同匹配
        if (item1NormBrackets.equalsIgnoreCase(item2NormBrackets)) {
            return true;
        }
        
        // 移除特殊字符后完全相同匹配
        String item1NoSpecial = removeSpecialCharacters(item1);
        String item2NoSpecial = removeSpecialCharacters(item2);
        if (item1NoSpecial.equals(item2NoSpecial)) {
            return true;
        }
        
        // 移除特殊字符后不区分大小写完全相同匹配
        if (item1NoSpecial.equalsIgnoreCase(item2NoSpecial)) {
            return true;
        }
        
        // 去空格后完全相同匹配
        String item1NoSpace = removeWhitespace(item1);
        String item2NoSpace = removeWhitespace(item2);
        if (item1NoSpace.equals(item2NoSpace)) {
            return true;
        }
        
        // 去空格后不区分大小写完全相同匹配
        if (item1NoSpace.equalsIgnoreCase(item2NoSpace)) {
            return true;
        }
        
        // 如果一个包含另一个，则认为匹配成功
        if (item1.contains(item2) || item2.contains(item1)) {
            return true;
        }
        
        // 不区分大小写包含匹配
        String item1Lower = item1.toLowerCase();
        String item2Lower = item2.toLowerCase();
        if (item1Lower.contains(item2Lower) || item2Lower.contains(item1Lower)) {
            return true;
        }
        
        // 将检测项目分解为单个项目列表进行匹配
        Set<String> items1 = parseTestItems(item1);
        Set<String> items2 = parseTestItems(item2);
        
        // 如果两个列表有交集，则认为匹配成功
        for (String item : items1) {
            if (items2.contains(item)) {
                return true;
            }
        }
        
        // 不区分大小写的项目列表匹配
        Set<String> items1Lower = new HashSet<>();
        Set<String> items2Lower = new HashSet<>();
        for (String item : items1) {
            items1Lower.add(item.toLowerCase());
        }
        for (String item : items2) {
            items2Lower.add(item.toLowerCase());
        }
        
        for (String item : items1Lower) {
            if (items2Lower.contains(item)) {
                return true;
            }
        }
        
        // 模糊匹配：如果一个包含另一个
        for (String item1Full : items1) {
            for (String item2Full : items2) {
                if (item1Full.contains(item2Full) || item2Full.contains(item1Full)) {
                    return true;
                }
            }
        }
        
        return false;
    }
    
    /**
     * 标准化括号：将中文括号转换为英文括号
     */
    private String normalizeBrackets(String text) {
        return text.replace("（", "(").replace("）", ")");
    }
    
    /**
     * 移除特殊字符
     */
    private String removeSpecialCharacters(String text) {
        String result = text;
        for (String specialChar : MatchingConfig.getSpecialCharacters()) {
            result = result.replace(specialChar, "");
        }
        return result;
    }
    
    /**
     * 移除空格
     */
    private String removeWhitespace(String text) {
        return text.replaceAll("\\s+", "");
    }
    
    /**
     * 解析检测项目字符串为单个项目集合
     */
    private Set<String> parseTestItems(String testItems) {
        Set<String> items = new HashSet<>();
        
        // 按常见分隔符分割
        String[] separators = {",", "，", ";", "；", "+", "、", "|"};
        String[] parts = {testItems};
        
        for (String separator : separators) {
            Set<String> newParts = new HashSet<>();
            for (String part : parts) {
                String[] splitParts = part.split("\\" + separator);
                for (String splitPart : splitParts) {
                    newParts.add(splitPart.trim());
                }
            }
            parts = newParts.toArray(new String[0]);
        }
        
        for (String part : parts) {
            if (!part.trim().isEmpty()) {
                items.add(part.trim());
            }
        }
        
        return items;
    }
    
    /**
     * 判断值是否为空
     */
    private boolean isEmptyValue(String value) {
        return value == null || value.trim().isEmpty() || "null".equalsIgnoreCase(value.trim());
    }
}
