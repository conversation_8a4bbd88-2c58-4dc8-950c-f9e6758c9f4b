package cn.naii.analysis.matcher;

import cn.naii.analysis.config.MatchingConfig;

/**
 * 年龄匹配器
 * 实现年龄的模糊匹配，允许一定的误差范围
 */
public class AgeMatcher implements FieldMatcher {
    
    @Override
    public boolean matches(String labelValue, String predictValue) {
        if (isEmptyValue(labelValue) && isEmptyValue(predictValue)) {
            return true;
        }
        if (isEmptyValue(labelValue) || isEmptyValue(predictValue)) {
            return false;
        }
        
        return isAgeMatch(labelValue, predictValue);
    }
    
    @Override
    public String getFieldType() {
        return "年龄";
    }
    
    /**
     * 判断年龄是否匹配
     * 允许一定的误差范围
     */
    private boolean isAgeMatch(String labelValue, String predictValue) {
        try {
            // 处理可能的null或"null"字符串
            if ("null".equalsIgnoreCase(labelValue) || "null".equalsIgnoreCase(predictValue)) {
                return "null".equalsIgnoreCase(labelValue) && "null".equalsIgnoreCase(predictValue);
            }
            
            int labelAge = Integer.parseInt(labelValue);
            
            // 处理预测值中可能包含"岁"字的情况
            String cleanPredictValue = predictValue;
            if (predictValue.contains("岁")) {
                cleanPredictValue = predictValue.substring(0, predictValue.indexOf("岁"));
            }
            
            int predictAge = Integer.parseInt(cleanPredictValue);
            
            // 允许年龄有指定误差范围
            return Math.abs(labelAge - predictAge) <= MatchingConfig.getAgeTolerance();
        } catch (NumberFormatException e) {
            // 如果无法解析为数字，则直接比较字符串
            return labelValue.equals(predictValue);
        }
    }
    
    /**
     * 判断值是否为空
     */
    private boolean isEmptyValue(String value) {
        return value == null || value.trim().isEmpty() || "null".equalsIgnoreCase(value.trim());
    }
}
