package cn.naii.analysis.matcher;

/**
 * 包含匹配器
 * 用于医生、样本类型等字段的包含匹配
 */
public class ContainsMatcher implements FieldMatcher {
    
    private final String fieldType;
    
    public ContainsMatcher(String fieldType) {
        this.fieldType = fieldType;
    }
    
    @Override
    public boolean matches(String labelValue, String predictValue) {
        if (isEmptyValue(labelValue) && isEmptyValue(predictValue)) {
            return true;
        }
        if (isEmptyValue(labelValue) || isEmptyValue(predictValue)) {
            return false;
        }
        
        // 双向包含匹配
        return labelValue.contains(predictValue) || predictValue.contains(labelValue);
    }
    
    @Override
    public String getFieldType() {
        return fieldType;
    }
    
    /**
     * 判断值是否为空
     */
    private boolean isEmptyValue(String value) {
        return value == null || value.trim().isEmpty() || "null".equalsIgnoreCase(value.trim());
    }
}
