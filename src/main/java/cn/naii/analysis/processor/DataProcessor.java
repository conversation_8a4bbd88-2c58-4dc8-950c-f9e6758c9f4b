package cn.naii.analysis.processor;

import cn.naii.analysis.model.PredictionData;
import com.alibaba.fastjson2.JSONObject;

import java.util.List;

/**
 * 数据处理器接口
 * 定义数据处理的通用接口
 */
public interface DataProcessor {
    
    /**
     * 处理预测数据列表
     * 
     * @param predictionDataList 原始预测数据列表
     * @return 处理后的数据列表
     */
    List<ProcessedSample> processData(List<PredictionData> predictionDataList);
    
    /**
     * 清理JSON字符串
     * 
     * @param jsonString 原始JSON字符串
     * @return 清理后的JSON字符串
     */
    String sanitizeJsonString(String jsonString);
    
    /**
     * 解析JSON字符串为JSONObject
     * 
     * @param jsonString JSON字符串
     * @return JSONObject实例
     * @throws Exception 解析异常
     */
    JSONObject parseJsonObject(String jsonString) throws Exception;
}
