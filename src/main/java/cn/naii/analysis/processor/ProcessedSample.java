package cn.naii.analysis.processor;

import com.alibaba.fastjson2.JSONObject;

/**
 * 处理后的样本数据
 * 包含解析后的预测和标签JSON对象
 */
public class ProcessedSample {
    
    private final int sampleIndex;
    private final JSONObject predictObject;
    private final JSONObject labelObject;
    private final boolean isValid;
    private final String errorMessage;
    
    /**
     * 构造有效的样本数据
     */
    public ProcessedSample(int sampleIndex, JSONObject predictObject, JSONObject labelObject) {
        this.sampleIndex = sampleIndex;
        this.predictObject = predictObject;
        this.labelObject = labelObject;
        this.isValid = true;
        this.errorMessage = null;
    }
    
    /**
     * 构造无效的样本数据
     */
    public ProcessedSample(int sampleIndex, String errorMessage) {
        this.sampleIndex = sampleIndex;
        this.predictObject = null;
        this.labelObject = null;
        this.isValid = false;
        this.errorMessage = errorMessage;
    }
    
    public int getSampleIndex() {
        return sampleIndex;
    }
    
    public JSONObject getPredictObject() {
        return predictObject;
    }
    
    public JSONObject getLabelObject() {
        return labelObject;
    }
    
    public boolean isValid() {
        return isValid;
    }
    
    public String getErrorMessage() {
        return errorMessage;
    }
    
    /**
     * 获取样本编号（从1开始）
     */
    public int getSampleNumber() {
        return sampleIndex + 1;
    }
}
