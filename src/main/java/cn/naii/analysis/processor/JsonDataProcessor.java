package cn.naii.analysis.processor;

import cn.naii.analysis.model.PredictionData;
import com.alibaba.fastjson2.JSONObject;

import java.util.ArrayList;
import java.util.List;

/**
 * JSON数据处理器实现
 * 负责处理和清理JSON数据
 */
public class JsonDataProcessor implements DataProcessor {
    
    @Override
    public List<ProcessedSample> processData(List<PredictionData> predictionDataList) {
        List<ProcessedSample> processedSamples = new ArrayList<>();
        
        for (int i = 0; i < predictionDataList.size(); i++) {
            PredictionData predictionData = predictionDataList.get(i);
            
            try {
                // 清理JSON字符串
                String predictJson = sanitizeJsonString(predictionData.getPredict());
                String labelJson = sanitizeJsonString(predictionData.getLabel());
                
                // 替换NaN为null
                predictJson = predictJson.replace("NaN", "null");
                labelJson = labelJson.replace("NaN", "null");
                
                // 解析JSON对象
                JSONObject predictObject = parseJsonObject(predictJson);
                JSONObject labelObject = parseJsonObject(labelJson);
                
                // 创建有效的处理样本
                processedSamples.add(new ProcessedSample(i, predictObject, labelObject));
                
            } catch (Exception e) {
                // 创建无效的处理样本
                String errorMessage = "Error parsing JSON: " + e.getMessage();
                processedSamples.add(new ProcessedSample(i, errorMessage));
            }
        }
        
        return processedSamples;
    }
    
    @Override
    public String sanitizeJsonString(String jsonString) {
        if (jsonString == null) {
            return null;
        }
        
        // 移除可能的BOM字符
        if (jsonString.startsWith("\uFEFF")) {
            jsonString = jsonString.substring(1);
        }
        
        // 移除前后空白字符
        jsonString = jsonString.trim();
        
        // 处理转义字符
        jsonString = jsonString.replace("\\\"", "\"");
        
        // 处理其他可能的问题字符
        jsonString = jsonString.replace("\r\n", "\\n");
        jsonString = jsonString.replace("\n", "\\n");
        jsonString = jsonString.replace("\r", "\\r");
        jsonString = jsonString.replace("\t", "\\t");
        
        return jsonString;
    }
    
    @Override
    public JSONObject parseJsonObject(String jsonString) throws Exception {
        if (jsonString == null || jsonString.trim().isEmpty()) {
            throw new IllegalArgumentException("JSON string is null or empty");
        }
        
        try {
            return JSONObject.parseObject(jsonString);
        } catch (Exception e) {
            throw new Exception("Failed to parse JSON: " + e.getMessage(), e);
        }
    }
}
