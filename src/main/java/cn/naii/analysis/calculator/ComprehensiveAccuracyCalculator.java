package cn.naii.analysis.calculator;

import cn.naii.analysis.config.FieldConfig;
import cn.naii.analysis.matcher.FieldMatcher;
import cn.naii.analysis.matcher.MatcherFactory;
import cn.naii.analysis.model.AccuracyResult;
import cn.naii.analysis.processor.ProcessedSample;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 综合准确率计算器
 * 实现完整的准确率计算逻辑，包括文档字段和患者信息字段
 */
public class ComprehensiveAccuracyCalculator implements AccuracyCalculator {

    // 全局统计数据
    private int allSamplesTotalComparisons = 0;
    private int allSamplesMatchedComparisons = 0;
    private final Map<String, FieldComparisonResult> fieldResults = new HashMap<>();

    @Override
    public AccuracyResult calculateAccuracy(List<ProcessedSample> processedSamples) {
        // 重置统计数据
        resetStatistics();

        // 初始化字段结果
        initializeFieldResults();

        // 处理每个样本
        for (ProcessedSample sample : processedSamples) {
            if (sample.isValid()) {
                processSample(sample);
            }
        }

        // 计算最终结果
        return buildAccuracyResult();
    }

    @Override
    public String getCalculatorType() {
        return "Comprehensive Accuracy Calculator";
    }

    /**
     * 重置统计数据
     */
    private void resetStatistics() {
        allSamplesTotalComparisons = 0;
        allSamplesMatchedComparisons = 0;
        fieldResults.clear();
    }

    /**
     * 初始化字段结果
     */
    private void initializeFieldResults() {
        // 初始化患者信息字段
        for (String field : FieldConfig.getPatientFields()) {
            fieldResults.put(field, new FieldComparisonResult(field));
        }

        // 初始化文档字段（排除日期字段）
        for (String field : FieldConfig.getLabelDocumentFields()) {
            if (!FieldConfig.isExcludedField(field)) {
                fieldResults.put(field, new FieldComparisonResult(field));
            }
        }
    }

    /**
     * 处理单个样本
     */
    private void processSample(ProcessedSample sample) {
        JSONObject predictObject = sample.getPredictObject();
        JSONObject labelObject = sample.getLabelObject();
        int sampleIndex = sample.getSampleIndex();

        // 处理文档字段
        processDocumentFields(predictObject, labelObject, sampleIndex);

        // 处理患者信息字段
        processPatientFields(predictObject, labelObject, sampleIndex);
    }

    /**
     * 处理文档字段
     */
    private void processDocumentFields(JSONObject predictObject, JSONObject labelObject, int sampleIndex) {
        // 处理预测对象中的日期字段（不计入准确率）
        for (String field : FieldConfig.getPredictDocumentFields()) {
            if (FieldConfig.isExcludedField(field)) {
                continue; // 跳过排除的字段
            }

            String predictValue = getStringValue(predictObject, field);
            String labelValue = getStringValue(labelObject, field);

            processFieldComparison(field, labelValue, predictValue, sampleIndex, null);
        }

        // 处理只在标签对象中存在的字段
        for (String field : FieldConfig.getLabelDocumentFields()) {
            if (FieldConfig.isExcludedField(field)) {
                continue; // 跳过排除的字段
            }

            // 这些字段只在标签中存在，预测值为空
            String labelValue = getStringValue(labelObject, field);
            if (!ComparisonData.isEmptyValue(labelValue)) {
                // 只显示，不进行匹配计算
                // 这里可以添加日志记录或其他处理
            }
        }
    }

    /**
     * 处理患者信息字段
     */
    private void processPatientFields(JSONObject predictObject, JSONObject labelObject, int sampleIndex) {
        JSONArray labelPatients = labelObject.getJSONArray("患者信息");
        JSONArray predictPatients = predictObject.getJSONArray("患者信息");

        if (labelPatients == null || predictPatients == null) {
            return;
        }

        int minSize = Math.min(labelPatients.size(), predictPatients.size());

        // 处理共同部分（按顺序一一对应）
        for (int i = 0; i < minSize; i++) {
            JSONObject labelPatient = labelPatients.getJSONObject(i);
            JSONObject predictPatient = predictPatients.getJSONObject(i);
            String patientName = labelPatient.getString("患者姓名");

            // 处理每个字段
            for (String field : FieldConfig.getPatientFields()) {
                if (FieldConfig.isExcludedField(field)) {
                    continue; // 跳过排除的字段
                }

                String labelValue = getStringValue(labelPatient, field);
                String predictValue = getStringValue(predictPatient, field);

                processFieldComparison(field, labelValue, predictValue, sampleIndex, patientName);
            }
        }

        // 处理标签中多出的患者（未找到匹配）
        for (int i = minSize; i < labelPatients.size(); i++) {
            JSONObject labelPatient = labelPatients.getJSONObject(i);
            String patientName = labelPatient.getString("患者姓名");

            for (String field : FieldConfig.getPatientFields()) {
                if (FieldConfig.isExcludedField(field)) {
                    continue; // 跳过排除的字段
                }

                String labelValue = getStringValue(labelPatient, field);
                if (!ComparisonData.isEmptyValue(labelValue)) {
                    // 未找到匹配的情况
                    processFieldComparison(field, labelValue, null, sampleIndex, patientName);
                }
            }
        }
    }

    /**
     * 处理字段比较
     */
    private void processFieldComparison(String fieldName, String labelValue, String predictValue,
                                      int sampleIndex, String patientName) {
        FieldMatcher matcher = MatcherFactory.getMatcher(fieldName);
        boolean isMatch = matcher.matches(labelValue, predictValue);

        // 判断是否应该计入统计
        boolean shouldCount = shouldCountComparison(labelValue, predictValue);

        // 如果预测值不为空，标签值为空，则认为匹配失败
        if (!ComparisonData.isEmptyValue(predictValue) && ComparisonData.isEmptyValue(labelValue)) {
            isMatch = false;
        }

        // 创建比较数据
        ComparisonData comparisonData = new ComparisonData(
            fieldName, labelValue, predictValue, isMatch, shouldCount, sampleIndex, patientName);

        // 更新字段结果
        FieldComparisonResult fieldResult = fieldResults.get(fieldName);
        if (fieldResult != null) {
            fieldResult.addComparison(comparisonData);
        }

        // 更新全局统计
        if (shouldCount) {
            allSamplesTotalComparisons++;
            if (isMatch) {
                allSamplesMatchedComparisons++;
            }
        }
    }

    /**
     * 判断比较是否应该计入统计
     */
    private boolean shouldCountComparison(String labelValue, String predictValue) {
        // 如果两个值都为空，则不计入统计
        return !(ComparisonData.isEmptyValue(labelValue) && ComparisonData.isEmptyValue(predictValue));
    }

    /**
     * 获取字符串值
     */
    private String getStringValue(JSONObject jsonObject, String key) {
        if (jsonObject == null || !jsonObject.containsKey(key)) {
            return null;
        }
        Object value = jsonObject.get(key);
        return value != null ? value.toString() : null;
    }

    /**
     * 构建准确率结果
     */
    private AccuracyResult buildAccuracyResult() {
        // 计算各字段准确率
        Map<String, Double> fieldAccuracies = new HashMap<>();
        for (FieldComparisonResult result : fieldResults.values()) {
            fieldAccuracies.put(result.getFieldName(), result.getAccuracy());
        }

        // 计算总体准确率
        double overallAccuracy = allSamplesTotalComparisons > 0 ?
            (double) allSamplesMatchedComparisons / allSamplesTotalComparisons : 0.0;

        return new AccuracyResult(
            overallAccuracy,
            fieldAccuracies,
            allSamplesTotalComparisons,
            allSamplesMatchedComparisons,
            overallAccuracy,
            allSamplesTotalComparisons,
            allSamplesMatchedComparisons,
            new HashMap<>(fieldResults)
        );
    }
}
