package cn.naii.analysis.calculator;

import cn.naii.analysis.model.AccuracyResult;
import cn.naii.analysis.processor.ProcessedSample;

import java.util.List;

/**
 * 准确率计算器接口
 * 定义准确率计算的通用接口
 */
public interface AccuracyCalculator {
    
    /**
     * 计算准确率
     * 
     * @param processedSamples 处理后的样本数据列表
     * @return 准确率计算结果
     */
    AccuracyResult calculateAccuracy(List<ProcessedSample> processedSamples);
    
    /**
     * 获取计算器类型
     * 
     * @return 计算器类型描述
     */
    String getCalculatorType();
}
