package cn.naii.analysis.calculator;

import java.util.ArrayList;
import java.util.List;

/**
 * 字段比较结果类
 * 用于存储单个字段的比较统计结果
 */
public class FieldComparisonResult {
    
    private final String fieldName;
    private int totalCount;
    private int matchCount;
    private final List<String> errorDetails;
    
    public FieldComparisonResult(String fieldName) {
        this.fieldName = fieldName;
        this.totalCount = 0;
        this.matchCount = 0;
        this.errorDetails = new ArrayList<>();
    }
    
    /**
     * 添加比较结果
     */
    public void addComparison(ComparisonData comparisonData) {
        if (comparisonData.shouldCount()) {
            totalCount++;
            if (comparisonData.isMatch()) {
                matchCount++;
            } else {
                // 添加错误详情
                String errorDetail = String.format("样本 #%d: %s: %s vs %s",
                    comparisonData.getSampleNumber(),
                    comparisonData.getPatientName() != null ? comparisonData.getPatientName() : "",
                    comparisonData.getDisplayLabelValue(),
                    comparisonData.getDisplayPredictValue());
                errorDetails.add(errorDetail);
            }
        }
    }
    
    /**
     * 计算准确率
     */
    public double getAccuracy() {
        return totalCount > 0 ? (double) matchCount / totalCount : 0.0;
    }
    
    /**
     * 获取错误数量
     */
    public int getErrorCount() {
        return totalCount - matchCount;
    }
    
    public String getFieldName() {
        return fieldName;
    }
    
    public int getTotalCount() {
        return totalCount;
    }
    
    public int getMatchCount() {
        return matchCount;
    }
    
    public List<String> getErrorDetails() {
        return new ArrayList<>(errorDetails);
    }
}
