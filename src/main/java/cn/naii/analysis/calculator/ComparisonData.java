package cn.naii.analysis.calculator;

/**
 * 比较数据类
 * 用于存储字段比较的相关信息
 */
public class ComparisonData {
    
    private final String fieldName;
    private final String labelValue;
    private final String predictValue;
    private final boolean isMatch;
    private final boolean shouldCount;
    private final int sampleIndex;
    private final String patientName;
    
    public ComparisonData(String fieldName, String labelValue, String predictValue, 
                         boolean isMatch, boolean shouldCount, int sampleIndex, String patientName) {
        this.fieldName = fieldName;
        this.labelValue = labelValue;
        this.predictValue = predictValue;
        this.isMatch = isMatch;
        this.shouldCount = shouldCount;
        this.sampleIndex = sampleIndex;
        this.patientName = patientName;
    }
    
    public String getFieldName() {
        return fieldName;
    }
    
    public String getLabelValue() {
        return labelValue;
    }
    
    public String getPredictValue() {
        return predictValue;
    }
    
    public boolean isMatch() {
        return isMatch;
    }
    
    public boolean shouldCount() {
        return shouldCount;
    }
    
    public int getSampleIndex() {
        return sampleIndex;
    }
    
    public String getPatientName() {
        return patientName;
    }
    
    /**
     * 获取样本编号（从1开始）
     */
    public int getSampleNumber() {
        return sampleIndex + 1;
    }
    
    /**
     * 判断值是否为空
     */
    public static boolean isEmptyValue(String value) {
        return value == null || value.trim().isEmpty() || "null".equalsIgnoreCase(value.trim());
    }
    
    /**
     * 获取显示用的标签值
     */
    public String getDisplayLabelValue() {
        return isEmptyValue(labelValue) ? "空值" : labelValue;
    }
    
    /**
     * 获取显示用的预测值
     */
    public String getDisplayPredictValue() {
        return isEmptyValue(predictValue) ? "未找到匹配" : predictValue;
    }
}
