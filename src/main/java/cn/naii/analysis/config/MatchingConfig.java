package cn.naii.analysis.config;

/**
 * 匹配规则配置类
 * 管理字段匹配时使用的各种配置参数
 */
public class MatchingConfig {
    
    /**
     * 特殊字符数组，用于检测项目匹配时过滤
     */
    public static final String[] SPECIAL_CHARACTERS = {"|", "-"};
    
    /**
     * 年龄匹配允许的误差范围
     */
    public static final int AGE_TOLERANCE = 1;
    
    /**
     * 姓名相似度匹配的编辑距离阈值比例
     */
    public static final double NAME_SIMILARITY_THRESHOLD = 1.0 / 3.0;
    
    /**
     * 字符匹配度阈值（用于模糊匹配）
     */
    public static final double CHARACTER_MATCH_THRESHOLD = 0.5;
    
    /**
     * 获取特殊字符数组
     */
    public static String[] getSpecialCharacters() {
        return SPECIAL_CHARACTERS.clone();
    }
    
    /**
     * 获取年龄容差
     */
    public static int getAgeTolerance() {
        return AGE_TOLERANCE;
    }
    
    /**
     * 获取姓名相似度阈值
     */
    public static double getNameSimilarityThreshold() {
        return NAME_SIMILARITY_THRESHOLD;
    }
    
    /**
     * 获取字符匹配度阈值
     */
    public static double getCharacterMatchThreshold() {
        return CHARACTER_MATCH_THRESHOLD;
    }
}
