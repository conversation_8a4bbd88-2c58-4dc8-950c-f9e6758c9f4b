package cn.naii.analysis.config;

/**
 * 字段配置管理类
 * 管理所有用于数据分析的字段定义
 */
public class FieldConfig {
    
    /**
     * 患者信息字段定义
     */
    public static final String[] PATIENT_FIELDS = {
        "条形码", "患者姓名", "年龄", "性别", "住院/门诊号", 
        "床位", "科室", "样本类型", "样本性状", "采集时间", 
        "医生", "检测项目", "备注栏"
    };
    
    /**
     * 预测对象文档字段
     */
    public static final String[] PREDICT_DOCUMENT_FIELDS = {"日期"};
    
    /**
     * 标签对象文档字段
     */
    public static final String[] LABEL_DOCUMENT_FIELDS = {"日期"};
    
    /**
     * 不计入准确率统计的字段
     */
    public static final String[] EXCLUDED_FIELDS = {"日期"};
    
    /**
     * 获取患者信息字段
     */
    public static String[] getPatientFields() {
        return PATIENT_FIELDS.clone();
    }
    
    /**
     * 获取预测文档字段
     */
    public static String[] getPredictDocumentFields() {
        return PREDICT_DOCUMENT_FIELDS.clone();
    }
    
    /**
     * 获取标签文档字段
     */
    public static String[] getLabelDocumentFields() {
        return LABEL_DOCUMENT_FIELDS.clone();
    }
    
    /**
     * 检查字段是否应该被排除在准确率统计之外
     */
    public static boolean isExcludedField(String field) {
        for (String excludedField : EXCLUDED_FIELDS) {
            if (excludedField.equals(field)) {
                return true;
            }
        }
        return false;
    }
}
