package cn.naii.analysis.model;

import cn.naii.analysis.calculator.FieldComparisonResult;

import java.util.Map;

/**
 * Model class to represent accuracy calculation results
 */
public class AccuracyResult {
    private double overallAccuracy;
    private Map<String, Double> fieldAccuracies;
    private int totalComparisons;
    private int matchedComparisons;
    private double allSamplesAccuracy; // 所有样本的总体准确率
    private int allSamplesTotalComparisons; // 所有样本的总比较项数
    private int allSamplesMatchedComparisons; // 所有样本的匹配项数
    private Map<String, FieldComparisonResult> fieldResults; // 字段详细结果

    public AccuracyResult(double overallAccuracy, Map<String, Double> fieldAccuracies,
                         int totalComparisons, int matchedComparisons) {
        this.overallAccuracy = overallAccuracy;
        this.fieldAccuracies = fieldAccuracies;
        this.totalComparisons = totalComparisons;
        this.matchedComparisons = matchedComparisons;
        this.allSamplesAccuracy = 0;
        this.allSamplesTotalComparisons = 0;
        this.allSamplesMatchedComparisons = 0;
        this.fieldResults = null;
    }

    public AccuracyResult(double overallAccuracy, Map<String, Double> fieldAccuracies,
                         int totalComparisons, int matchedComparisons,
                         double allSamplesAccuracy, int allSamplesTotalComparisons, int allSamplesMatchedComparisons,
                         Map<String, FieldComparisonResult> fieldResults) {
        this.overallAccuracy = overallAccuracy;
        this.fieldAccuracies = fieldAccuracies;
        this.totalComparisons = totalComparisons;
        this.matchedComparisons = matchedComparisons;
        this.allSamplesAccuracy = allSamplesAccuracy;
        this.allSamplesTotalComparisons = allSamplesTotalComparisons;
        this.allSamplesMatchedComparisons = allSamplesMatchedComparisons;
        this.fieldResults = fieldResults;
    }

    public double getOverallAccuracy() {
        return overallAccuracy;
    }

    public Map<String, Double> getFieldAccuracies() {
        return fieldAccuracies;
    }

    public int getTotalComparisons() {
        return totalComparisons;
    }

    public int getMatchedComparisons() {
        return matchedComparisons;
    }

    public double getAllSamplesAccuracy() {
        return allSamplesAccuracy;
    }

    public int getAllSamplesTotalComparisons() {
        return allSamplesTotalComparisons;
    }

    public int getAllSamplesMatchedComparisons() {
        return allSamplesMatchedComparisons;
    }

    public void setAllSamplesAccuracy(double allSamplesAccuracy) {
        this.allSamplesAccuracy = allSamplesAccuracy;
    }

    public void setAllSamplesTotalComparisons(int allSamplesTotalComparisons) {
        this.allSamplesTotalComparisons = allSamplesTotalComparisons;
    }

    public void setAllSamplesMatchedComparisons(int allSamplesMatchedComparisons) {
        this.allSamplesMatchedComparisons = allSamplesMatchedComparisons;
    }

    public Map<String, FieldComparisonResult> getFieldResults() {
        return fieldResults;
    }

    public void setFieldResults(Map<String, FieldComparisonResult> fieldResults) {
        this.fieldResults = fieldResults;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("准确率统计结果:\n");
        sb.append(String.format("当前样本准确率: %.2f%% (%d/%d)\n", overallAccuracy * 100, matchedComparisons, totalComparisons));

        // 添加所有样本的总体准确率
        if (allSamplesTotalComparisons > 0) {
            sb.append(String.format("所有样本总体准确率: %.2f%% (%d/%d)\n",
                    allSamplesAccuracy * 100, allSamplesMatchedComparisons, allSamplesTotalComparisons));
        }

        sb.append("各字段准确率:\n");

        fieldAccuracies.forEach((field, accuracy) -> {
            sb.append(String.format("- %s: %.2f%%\n", field, accuracy * 100));
        });

        return sb.toString();
    }
}
