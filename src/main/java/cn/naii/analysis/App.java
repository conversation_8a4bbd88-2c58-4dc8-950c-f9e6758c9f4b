package cn.naii.analysis;

import cn.naii.analysis.calculator.ComprehensiveAccuracyCalculator;
import cn.naii.analysis.formatter.DetailedResultFormatter;
import cn.naii.analysis.model.PredictionData;
import cn.naii.analysis.processor.JsonDataProcessor;
import cn.naii.analysis.service.JsonFileReader;
import cn.naii.analysis.service.PredictionAnalysisService;

import java.util.List;

/**
 * Application to read and process JSON prediction data
 * 重构后的主应用类，使用模块化架构
 */
public class App {

    public static void main(String[] args) {
        // Path to the JSON file
        String filePath = "src/main/resources/json/new/generated_predictions2.jsonl";

        // Create components using dependency injection pattern
        JsonFileReader jsonFileReader = new JsonFileReader();
        JsonDataProcessor dataProcessor = new JsonDataProcessor();
        ComprehensiveAccuracyCalculator accuracyCalculator = new ComprehensiveAccuracyCalculator();
        DetailedResultFormatter resultFormatter = new DetailedResultFormatter();

        // Create the main service
        PredictionAnalysisService analysisService = new PredictionAnalysisService(
            dataProcessor, accuracyCalculator);

        // Read the prediction data
        List<PredictionData> predictionDataList = jsonFileReader.readPredictionData(filePath);

        // Analyze the predictions
        PredictionAnalysisService.AnalysisResult analysisResult =
            analysisService.analyzePredictions(predictionDataList);

        // Format and output the results
        resultFormatter.formatAndOutput(analysisResult);
    }
}
