package cn.naii.analysis.formatter;

import cn.naii.analysis.processor.ProcessedSample;
import cn.naii.analysis.service.PredictionAnalysisService.AnalysisResult;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;

/**
 * 控制台结果格式化器
 * 负责将分析结果格式化输出到控制台
 */
public class ConsoleResultFormatter implements ResultFormatter {
    
    @Override
    public void formatAndOutput(AnalysisResult analysisResult) {
        System.out.println("Total predictions read: " + analysisResult.getProcessedSamples().size());
        System.out.println("\nSample of predictions:");
        
        // 输出每个样本的详细信息
        for (ProcessedSample sample : analysisResult.getProcessedSamples()) {
            if (sample.isValid()) {
                System.out.println("\n============= 样本 #" + sample.getSampleNumber() + " ==============");
                System.out.println(formatSampleDetails(sample));
            } else {
                System.out.println("\n============= 错误样本 #" + sample.getSampleNumber() + " ==============");
                System.err.println(sample.getErrorMessage());
            }
        }
        
        // 输出总体准确率
        System.out.println("\n\n================================================");
        System.out.println(analysisResult.getAccuracyResult());
        System.out.println("================================================");
    }
    
    @Override
    public String formatSampleDetails(ProcessedSample sample) {
        if (!sample.isValid()) {
            return "Invalid sample: " + sample.getErrorMessage();
        }
        
        StringBuilder sb = new StringBuilder();
        
        // 格式化标签数据
        String labelInfo = formatObjectInfo("label 真实数据结果:", sample.getLabelObject());
        sb.append(labelInfo);
        
        // 格式化预测数据
        String predictInfo = formatObjectInfo("predict 模型识别结果:", sample.getPredictObject());
        sb.append(predictInfo);
        
        return sb.toString();
    }
    
    @Override
    public String getFormatterType() {
        return "Console Result Formatter";
    }
    
    /**
     * 格式化JSON对象信息
     */
    private String formatObjectInfo(String label, JSONObject jsonObject) {
        StringBuilder sb = new StringBuilder();
        
        // 处理图片地址和日期
        String imageInfo = formatImageAndDate(label, jsonObject);
        sb.append(imageInfo);
        
        // 处理患者信息
        JSONArray patientArray = jsonObject.getJSONArray("患者信息");
        if (patientArray != null) {
            sb.append("【");
            for (int i = 0; i < patientArray.size(); i++) {
                JSONObject patient = patientArray.getJSONObject(i);
                sb.append("\n").append(formatPatientInfo(patient));
            }
            sb.append("】\n");
        }
        
        return sb.toString();
    }
    
    /**
     * 格式化图片地址和日期信息
     */
    private String formatImageAndDate(String label, JSONObject jsonObject) {
        StringBuilder sb = new StringBuilder();
        String date = jsonObject.getString("日期");
        
        if ("label 真实数据结果:".equals(label)) {
            String image = jsonObject.getString("图片地址");
            if (image != null) {
                String imageName = image.substring(image.lastIndexOf("\\") + 1, image.lastIndexOf("."));
                sb.append(label).append("图片地址: ").append(imageName).append(" 日期: ").append(date);
            } else {
                sb.append(label).append("图片地址: null 日期: ").append(date);
            }
        } else {
            sb.append(label).append(" 日期: ").append(date);
        }
        sb.append("\n");
        
        return sb.toString();
    }
    
    /**
     * 格式化患者信息
     */
    private String formatPatientInfo(JSONObject patient) {
        return String.format("条形码: %s|患者姓名: %s |年龄: %s |性别: %s |住院/门诊号: %s |床位: %s |科室: %s |采集时间: %s |医生: %s |样本类型: %s |样本性状: %s |检测项目: %s |备注栏: %s",
            patient.getString("条形码"),
            patient.getString("患者姓名"),
            patient.getString("年龄"),
            patient.getString("性别"),
            patient.getString("住院/门诊号"),
            patient.getString("床位"),
            patient.getString("科室"),
            patient.getString("采集时间"),
            patient.getString("医生"),
            patient.getString("样本类型"),
            patient.getString("样本性状"),
            patient.getString("检测项目"),
            patient.getString("备注栏"));
    }
}
