package cn.naii.analysis.formatter;

import cn.naii.analysis.calculator.FieldComparisonResult;
import cn.naii.analysis.model.AccuracyResult;
import cn.naii.analysis.processor.ProcessedSample;
import cn.naii.analysis.service.PredictionAnalysisService.AnalysisResult;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 详细结果格式化器
 * 负责输出完整的准确率统计和错误详情
 */
public class DetailedResultFormatter implements ResultFormatter {
    
    @Override
    public void formatAndOutput(AnalysisResult analysisResult) {
        System.out.println("Total predictions read: " + analysisResult.getProcessedSamples().size());
        System.out.println("\nSample of predictions:");
        
        // 输出每个样本的详细信息
        for (ProcessedSample sample : analysisResult.getProcessedSamples()) {
            if (sample.isValid()) {
                System.out.println("\n============= 样本 #" + sample.getSampleNumber() + " ==============");
                System.out.println(formatSampleDetails(sample));
            } else {
                System.out.println("\n============= 错误样本 #" + sample.getSampleNumber() + " ==============");
                System.err.println(sample.getErrorMessage());
            }
        }
        
        // 输出详细的准确率统计
        outputDetailedAccuracyStatistics(analysisResult.getAccuracyResult());
    }
    
    @Override
    public String formatSampleDetails(ProcessedSample sample) {
        if (!sample.isValid()) {
            return "Invalid sample: " + sample.getErrorMessage();
        }
        
        StringBuilder sb = new StringBuilder();
        
        // 格式化标签数据
        String labelInfo = formatObjectInfo("label 真实数据结果:", sample.getLabelObject());
        sb.append(labelInfo);
        
        // 格式化预测数据
        String predictInfo = formatObjectInfo("predict 模型识别结果:", sample.getPredictObject());
        sb.append(predictInfo);
        
        return sb.toString();
    }
    
    @Override
    public String getFormatterType() {
        return "Detailed Result Formatter";
    }
    
    /**
     * 输出详细的准确率统计
     */
    private void outputDetailedAccuracyStatistics(AccuracyResult accuracyResult) {
        System.out.println("\n\n================================================");
        
        // 输出按字段的准确率
        outputFieldAccuracyStatistics(accuracyResult);
        
        // 输出按样本分组的错误详情
        outputSampleGroupedErrors(accuracyResult);
        
        System.out.println("\n================================================");
        // 计算并输出所有样本的总体准确率
        double allSamplesAccuracy = accuracyResult.getAllSamplesTotalComparisons() > 0 ?
                (double) accuracyResult.getAllSamplesMatchedComparisons() / accuracyResult.getAllSamplesTotalComparisons() : 0;
        System.out.println("所有样本总体准确率: " +
                String.format("%.2f%% (%d/%d)", allSamplesAccuracy * 100,
                        accuracyResult.getAllSamplesMatchedComparisons(), accuracyResult.getAllSamplesTotalComparisons()));
        System.out.println("================================================");
    }
    
    /**
     * 输出按字段的准确率统计
     */
    private void outputFieldAccuracyStatistics(AccuracyResult accuracyResult) {
        System.out.println("\n按字段的准确率:");
        System.out.println("------------------------------------------------");
        
        Map<String, FieldComparisonResult> fieldResults = accuracyResult.getFieldResults();
        if (fieldResults != null) {
            for (String field : fieldResults.keySet()) {
                FieldComparisonResult result = fieldResults.get(field);
                int errorCount = result.getErrorCount();
                
                System.out.printf("%s: %.2f%% (%d/%d)", 
                    field, 
                    result.getAccuracy() * 100, 
                    result.getMatchCount(), 
                    result.getTotalCount());
                
                if (errorCount > 0) {
                    System.out.printf(" - 错误数: %d", errorCount);
                }
                System.out.println();
                
                // 输出错误详情（限制显示前5个）
                List<String> errorDetails = result.getErrorDetails();
                if (!errorDetails.isEmpty()) {
                    int displayCount = Math.min(5, errorDetails.size());
                    for (int i = 0; i < displayCount; i++) {
                        System.out.println("  " + errorDetails.get(i));
                    }
                    if (errorDetails.size() > 5) {
                        System.out.println("  ... 还有 " + (errorDetails.size() - 5) + " 个错误");
                    }
                }
            }
        }
    }
    
    /**
     * 输出按样本分组的错误详情
     */
    private void outputSampleGroupedErrors(AccuracyResult accuracyResult) {
        Map<String, FieldComparisonResult> fieldResults = accuracyResult.getFieldResults();
        if (fieldResults == null) {
            return;
        }
        
        // 创建一个按样本分组的错误详情Map
        Map<Integer, Map<String, List<String>>> sampleErrorDetails = new HashMap<>();
        
        // 将错误详情按样本分组
        for (String field : fieldResults.keySet()) {
            FieldComparisonResult result = fieldResults.get(field);
            List<String> errors = result.getErrorDetails();
            for (String error : errors) {
                // 解析样本号
                if (error.startsWith("样本 #")) {
                    try {
                        int sampleIndex = Integer.parseInt(error.substring(4, error.indexOf(":"))) - 1;
                        
                        // 初始化样本的错误详情Map
                        if (!sampleErrorDetails.containsKey(sampleIndex)) {
                            sampleErrorDetails.put(sampleIndex, new HashMap<>());
                        }
                        
                        // 初始化字段的错误详情List
                        if (!sampleErrorDetails.get(sampleIndex).containsKey(field)) {
                            sampleErrorDetails.get(sampleIndex).put(field, new java.util.ArrayList<>());
                        }
                        
                        // 添加错误详情
                        sampleErrorDetails.get(sampleIndex).get(field).add(error);
                    } catch (NumberFormatException | StringIndexOutOfBoundsException e) {
                        // 忽略解析错误
                    }
                }
            }
        }
        
        // 输出按样本分组的错误详情（只显示前10个样本的错误）
        if (!sampleErrorDetails.isEmpty()) {
            System.out.println("\n按样本分组的错误详情 (显示前10个有错误的样本):");
            System.out.println("------------------------------------------------");
            
            int displayedSamples = 0;
            for (Integer sampleIndex : sampleErrorDetails.keySet()) {
                if (displayedSamples >= 10) {
                    System.out.println("... 还有更多样本的错误详情");
                    break;
                }
                
                Map<String, List<String>> sampleErrors = sampleErrorDetails.get(sampleIndex);
                System.out.println("样本 #" + (sampleIndex + 1) + " 的错误:");
                
                for (String field : sampleErrors.keySet()) {
                    List<String> fieldErrors = sampleErrors.get(field);
                    for (String error : fieldErrors) {
                        System.out.println("  " + field + ": " + error.substring(error.indexOf(":") + 1).trim());
                    }
                }
                System.out.println();
                displayedSamples++;
            }
        }
    }
    
    /**
     * 格式化JSON对象信息
     */
    private String formatObjectInfo(String label, JSONObject jsonObject) {
        StringBuilder sb = new StringBuilder();
        
        // 处理图片地址和日期
        String imageInfo = formatImageAndDate(label, jsonObject);
        sb.append(imageInfo);
        
        // 处理患者信息
        JSONArray patientArray = jsonObject.getJSONArray("患者信息");
        if (patientArray != null) {
            sb.append("【");
            for (int i = 0; i < patientArray.size(); i++) {
                JSONObject patient = patientArray.getJSONObject(i);
                sb.append("\n").append(formatPatientInfo(patient));
            }
            sb.append("】\n");
        }
        
        return sb.toString();
    }
    
    /**
     * 格式化图片地址和日期信息
     */
    private String formatImageAndDate(String label, JSONObject jsonObject) {
        StringBuilder sb = new StringBuilder();
        String date = jsonObject.getString("日期");
        
        if ("label 真实数据结果:".equals(label)) {
            String image = jsonObject.getString("图片地址");
            if (image != null) {
                String imageName = image.substring(image.lastIndexOf("\\") + 1, image.lastIndexOf("."));
                sb.append(label).append("图片地址: ").append(imageName).append(" 日期: ").append(date);
            } else {
                sb.append(label).append("图片地址: null 日期: ").append(date);
            }
        } else {
            sb.append(label).append(" 日期: ").append(date);
        }
        sb.append("\n");
        
        return sb.toString();
    }
    
    /**
     * 格式化患者信息
     */
    private String formatPatientInfo(JSONObject patient) {
        return String.format("条形码: %s|患者姓名: %s |年龄: %s |性别: %s |住院/门诊号: %s |床位: %s |科室: %s |采集时间: %s |医生: %s |样本类型: %s |样本性状: %s |检测项目: %s |备注栏: %s",
            patient.getString("条形码"),
            patient.getString("患者姓名"),
            patient.getString("年龄"),
            patient.getString("性别"),
            patient.getString("住院/门诊号"),
            patient.getString("床位"),
            patient.getString("科室"),
            patient.getString("采集时间"),
            patient.getString("医生"),
            patient.getString("样本类型"),
            patient.getString("样本性状"),
            patient.getString("检测项目"),
            patient.getString("备注栏"));
    }
}
