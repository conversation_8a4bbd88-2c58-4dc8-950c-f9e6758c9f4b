package cn.naii.analysis.formatter;

import cn.naii.analysis.processor.ProcessedSample;
import cn.naii.analysis.service.PredictionAnalysisService.AnalysisResult;

/**
 * 结果格式化器接口
 * 定义结果输出格式化的通用接口
 */
public interface ResultFormatter {
    
    /**
     * 格式化并输出分析结果
     * 
     * @param analysisResult 分析结果
     */
    void formatAndOutput(AnalysisResult analysisResult);
    
    /**
     * 格式化单个样本的详细信息
     * 
     * @param sample 处理后的样本
     * @return 格式化后的字符串
     */
    String formatSampleDetails(ProcessedSample sample);
    
    /**
     * 获取格式化器类型
     * 
     * @return 格式化器类型描述
     */
    String getFormatterType();
}
