package cn.naii.analysis.service;

import cn.naii.analysis.calculator.AccuracyCalculator;
import cn.naii.analysis.model.AccuracyResult;
import cn.naii.analysis.model.PredictionData;
import cn.naii.analysis.processor.DataProcessor;
import cn.naii.analysis.processor.ProcessedSample;

import java.util.List;

/**
 * 预测分析服务
 * 负责协调数据处理和准确率计算
 */
public class PredictionAnalysisService {
    
    private final DataProcessor dataProcessor;
    private final AccuracyCalculator accuracyCalculator;
    
    public PredictionAnalysisService(DataProcessor dataProcessor, AccuracyCalculator accuracyCalculator) {
        this.dataProcessor = dataProcessor;
        this.accuracyCalculator = accuracyCalculator;
    }
    
    /**
     * 分析预测数据
     * 
     * @param predictionDataList 原始预测数据列表
     * @return 分析结果
     */
    public AnalysisResult analyzePredictions(List<PredictionData> predictionDataList) {
        // 处理数据
        List<ProcessedSample> processedSamples = dataProcessor.processData(predictionDataList);
        
        // 计算准确率
        AccuracyResult accuracyResult = accuracyCalculator.calculateAccuracy(processedSamples);
        
        // 返回分析结果
        return new AnalysisResult(processedSamples, accuracyResult);
    }
    
    /**
     * 分析结果类
     */
    public static class AnalysisResult {
        private final List<ProcessedSample> processedSamples;
        private final AccuracyResult accuracyResult;
        
        public AnalysisResult(List<ProcessedSample> processedSamples, AccuracyResult accuracyResult) {
            this.processedSamples = processedSamples;
            this.accuracyResult = accuracyResult;
        }
        
        public List<ProcessedSample> getProcessedSamples() {
            return processedSamples;
        }
        
        public AccuracyResult getAccuracyResult() {
            return accuracyResult;
        }
    }
}
